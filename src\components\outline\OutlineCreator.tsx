
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus, Save, ArrowLeft } from 'lucide-react';
import { Character } from '@/types/firestore-models';
import { Outline } from '@/types/outline';
import OutlineForm from './OutlineForm';
import SceneForm from './SceneForm';
import SceneList from './SceneList';
import { useOutlineForm } from './useOutlineFormRefactored';

interface OutlineCreatorProps {
  projectId: string;
  existingOutline?: Outline;
  characters?: Character[];
  onOutlineUpdated: (outline: Outline) => void;
  onCancel: () => void;
}

const OutlineCreator: React.FC<OutlineCreatorProps> = ({
  projectId,
  existingOutline,
  characters,
  onOutlineUpdated,
  onCancel
}) => {
  const {
    outline,
    currentScene,
    characters: loadedCharacters,
    locations,
    saving,
    dialogOpen,
    editingSceneIndex,
    handleOutlineChange,
    handleSceneInputChange,
    handleCharacterSelect,
    handleLocationSelect,
    handlePuntoDeVistaSelect,
    handleContentChange,
    openDialog,
    closeDialog,
    handleDeleteScene,
    handleDragEnd,
    handleSubmit,
  } = useOutlineForm({
    projectId,
    outlineToEdit: existingOutline,
  });

  // Estado para controlar la vista de pantalla completa
  const [fullScreenMode, setFullScreenMode] = useState(false);

  // Función para abrir el formulario en pantalla completa
  const openFullScreenForm = (index: number | null = null) => {
    if (index !== null) {
      // Edit scene logic would go here
      openDialog(outline.scenes[index], index);
    } else {
      openDialog();
    }
    setFullScreenMode(true);
  };

  // Función para cerrar el formulario y volver a la vista normal
  const closeFullScreenForm = () => {
    setFullScreenMode(false);
    closeDialog();
  };

  // Función para guardar la escena y volver a la vista normal
  const saveSceneAndClose = () => {
    // This would trigger the scene confirmation logic
    closeFullScreenForm();
  };

  // Si estamos en modo pantalla completa, mostramos solo el formulario de escena
  if (fullScreenMode) {
    return (
      <div className="fixed inset-0 bg-background z-50 overflow-auto p-4 md:p-6 lg:p-8">
        <div className="max-w-5xl mx-auto space-y-6">
          <div className="flex items-center justify-between">
            <Button 
              variant="ghost" 
              onClick={closeFullScreenForm}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Volver
            </Button>
            <h2 className="text-2xl font-bold">
              {editingSceneIndex !== null ? 'Editar escena' : 'Añadir nueva escena'}
            </h2>
            <Button onClick={saveSceneAndClose}>
              {editingSceneIndex !== null ? 'Guardar cambios' : 'Añadir escena'}
            </Button>
          </div>
          
          <div className="bg-card rounded-lg border shadow-sm p-6">
            <SceneForm
              title={currentScene.title}
              description={currentScene.description}
              location={currentScene.location}
              selectedCharacters={currentScene.characters || []}
              characters={loadedCharacters}
              locations={locations}
              sceneDate={currentScene.date}
              sceneTime={currentScene.time}
              objetivoEscena={currentScene.objetivoEscena}
              tono={currentScene.tono}
              elementoSimbolico={currentScene.elementoSimbolico}
              desarrolloEmocional={currentScene.desarrolloEmocional}
              funcionHistoria={currentScene.funcionHistoria}
              puntoDeVista={currentScene.puntoDeVista}
              content={currentScene.content}
              onInputChange={handleSceneInputChange}
              onCharacterSelect={handleCharacterSelect}
              onLocationSelect={handleLocationSelect}
              onPuntoDeVistaSelect={handlePuntoDeVistaSelect}
              onContentChange={handleContentChange}
            />
          </div>
        </div>
      </div>
    );
  }

  // Vista normal del creador de escaletas
  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <OutlineForm
          title={outline.title}
          description={outline.description}
          onInputChange={handleOutlineChange}
        />
      </div>
      
      <div>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">Escenas</h3>
          <Button size="sm" onClick={() => openFullScreenForm()}>
            <Plus className="h-4 w-4 mr-2" />
            Añadir escena
          </Button>
        </div>

        <SceneList
          scenes={outline.scenes}
          onDragEnd={handleDragEnd}
          onEditScene={(index) => openFullScreenForm(index)}
          onDeleteScene={handleDeleteScene}
          onAddScene={() => openFullScreenForm()}
        />
      </div>
      
      <div className="flex justify-between pt-6">
        <Button variant="outline" onClick={onCancel}>Cancelar</Button>
        <Button onClick={handleSubmit} disabled={saving}>
          {saving ? (
            <>Guardando...</>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              {existingOutline ? 'Guardar cambios' : 'Crear escaleta'}
            </>
          )}
        </Button>
      </div>

      {/* Mantenemos el diálogo oculto para la lógica interna */}
      <Dialog open={dialogOpen && !fullScreenMode} onOpenChange={(open) => !fullScreenMode && (open ? openDialog() : closeDialog())}>
        <DialogContent className="hidden">
          {/* Contenido oculto */}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default OutlineCreator;
