import { lazy } from 'react';

// Lazy load de páginas principales
export const LazyIdeasPage = lazy(() => import('./IdeasPage'));
export const LazyCharacterPage = lazy(() => import('./CharacterPage'));
export const LazyCharacterDetailPage = lazy(() => import('./CharacterDetailPage'));
export const LazyLocationDetailPage = lazy(() => import('./LocationDetailPage'));
export const LazyProjectsPage = lazy(() => import('./ProjectsPage'));
export const LazyProjectDetailPage = lazy(() => import('./ProjectDetailPage'));
export const LazyOutlineDetailPage = lazy(() => import('./OutlineDetailPage'));
export const LazyAuthPage = lazy(() => import('./AuthPage'));

// Lazy load de componentes grandes
export const LazyCharacterCreator = lazy(() => import('../components/character/CharacterCreator'));
export const LazyOutlineCreator = lazy(() => import('../components/outline/OutlineCreator'));
export const LazyIdeasModule = lazy(() => import('../components/IdeasModule'));

// Lazy load de tabs de ProjectDetailPage
export const LazyCharactersTab = lazy(() => import('../components/project/CharactersTab'));
export const LazyLocationsTab = lazy(() => import('../components/project/LocationsTab'));
export const LazyOutlinesTab = lazy(() => import('../components/project/OutlinesTab'));
export const LazyDevelopmentTab = lazy(() => import('../components/project/DevelopmentTab'));
