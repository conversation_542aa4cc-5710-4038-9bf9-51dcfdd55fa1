
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Card, CardContent } from '@/components/ui/card';
import { Sword, Brain, Heart, Shield } from 'lucide-react';
import { Character } from '@/types/character';

interface AttributesFormProps {
  character: Character | Omit<Character, 'id'>;
  onSliderChange: (name: string, value: number[]) => void;
}

const AttributesForm = ({ character, onSliderChange }: AttributesFormProps) => {
  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="strength" className="flex items-center">
              <Sword className="mr-2 h-4 w-4 text-red-500" />
              Fuerza
            </Label>
            <span className="text-sm font-medium">{character.strength}%</span>
          </div>
          <Slider
            id="strength"
            min={0}
            max={100}
            step={1}
            value={[character.strength]}
            onValueChange={(value) => onSliderChange('strength', value)}
            className="cursor-pointer"
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Débil</span>
            <span>Fuerte</span>
          </div>
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="intelligence" className="flex items-center">
              <Brain className="mr-2 h-4 w-4 text-blue-500" />
              Inteligencia
            </Label>
            <span className="text-sm font-medium">{character.intelligence}%</span>
          </div>
          <Slider
            id="intelligence"
            min={0}
            max={100}
            step={1}
            value={[character.intelligence]}
            onValueChange={(value) => onSliderChange('intelligence', value)}
            className="cursor-pointer"
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Simple</span>
            <span>Genio</span>
          </div>
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="charisma" className="flex items-center">
              <Heart className="mr-2 h-4 w-4 text-pink-500" />
              Carisma
            </Label>
            <span className="text-sm font-medium">{character.charisma}%</span>
          </div>
          <Slider
            id="charisma"
            min={0}
            max={100}
            step={1}
            value={[character.charisma]}
            onValueChange={(value) => onSliderChange('charisma', value)}
            className="cursor-pointer"
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Antipático</span>
            <span>Cautivador</span>
          </div>
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="resilience" className="flex items-center">
              <Shield className="mr-2 h-4 w-4 text-amber-500" />
              Resiliencia
            </Label>
            <span className="text-sm font-medium">{character.resilience}%</span>
          </div>
          <Slider
            id="resilience"
            min={0}
            max={100}
            step={1}
            value={[character.resilience]}
            onValueChange={(value) => onSliderChange('resilience', value)}
            className="cursor-pointer"
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Frágil</span>
            <span>Inquebrantable</span>
          </div>
        </div>
      </div>
      
      <AttributeSummary character={character} />
    </div>
  );
};

interface AttributeSummaryProps {
  character: Character | Omit<Character, 'id'>;
}

const AttributeSummary = ({ character }: AttributeSummaryProps) => {
  const getPrimaryAttribute = () => {
    const { strength, intelligence, charisma, resilience } = character;
    const maxValue = Math.max(strength, intelligence, charisma, resilience);
    
    if (maxValue === strength) return "Fuerza";
    if (maxValue === intelligence) return "Inteligencia";
    if (maxValue === charisma) return "Carisma";
    return "Resiliencia";
  };
  
  const getCharacterType = () => {
    const { charisma, intelligence, strength } = character;
    
    if (charisma > intelligence && charisma > strength) return "Social";
    if (intelligence > charisma && intelligence > strength) return "Estratega";
    return "Combatiente";
  };
  
  return (
    <Card className="border-dashed">
      <CardContent className="pt-6">
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-2 bg-secondary/50 rounded-md">
            <p className="text-xs font-medium mb-1">Atributo Principal</p>
            <p className="font-semibold">{getPrimaryAttribute()}</p>
          </div>
          <div className="text-center p-2 bg-secondary/50 rounded-md">
            <p className="text-xs font-medium mb-1">Tipo de Personaje</p>
            <p className="font-semibold">{getCharacterType()}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AttributesForm;
