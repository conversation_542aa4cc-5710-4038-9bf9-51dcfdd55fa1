import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useFirebase } from '@/contexts/FirebaseContext';
import { useToast } from '@/hooks/use-toast';
import { Outline, OutlineScene } from '@/types/outline';
import { Character } from '@/types/firestore-models';
import { Location } from '@/types/location';
import { createOutline, updateOutline } from '@/lib/firebase/firestore/outlines';
import { getProjectCharacters } from '@/lib/firebase/firestore/characters';
import { getProjectLocations } from '@/lib/firebase/firestore/locations';
import { Timestamp } from 'firebase/firestore';

// Hook para cargar datos relacionados (personajes y localizaciones)
export const useRelatedData = (projectId: string) => {
  const [characters, setCharacters] = useState<Character[]>([]);
  const [locations, setLocations] = useState<Location[]>([]);
  const [loadingRelatedData, setLoadingRelatedData] = useState(true);

  useEffect(() => {
    const loadRelatedData = async () => {
      if (!projectId) {
        setLoadingRelatedData(false);
        return;
      }

      try {
        setLoadingRelatedData(true);
        const [charactersData, locationsData] = await Promise.all([
          getProjectCharacters(projectId),
          getProjectLocations(projectId)
        ]);

        setCharacters(charactersData);
        setLocations(locationsData);
      } catch (error) {
        console.error('Error loading related data:', error);
      } finally {
        setLoadingRelatedData(false);
      }
    };

    loadRelatedData();
  }, [projectId]);

  return { characters, locations, loadingRelatedData };
};

// Hook para manejar el estado del outline principal
export const useOutlineState = (initialOutline: Outline | null, projectId: string) => {
  const [outline, setOutline] = useState<Outline>(initialOutline || {
    title: '',
    description: '',
    scenes: [],
    projectId: projectId,
  } as Outline);

  const handleOutlineChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setOutline(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return { outline, setOutline, handleOutlineChange };
};

// Hook para manejar el estado del diálogo y la escena actual
export const useSceneDialogState = () => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingSceneIndex, setEditingSceneIndex] = useState<number | null>(null);
  const [currentScene, setCurrentScene] = useState<OutlineScene>({
    title: '',
    description: '',
    location: '',
    characters: [],
    date: '',
    time: '',
    objetivoEscena: '',
    tono: '',
    puntoDeVista: '',
    conflicto: '',
    notas: '',
    tipoEscena: '',
  });

  const openDialog = (scene?: OutlineScene, index?: number) => {
    if (scene) {
      // Convertir Timestamp a Date si es necesario
      const processedScene = {
        ...scene,
        date: scene.date instanceof Timestamp ? scene.date.toDate() : scene.date,
      };
      setCurrentScene(processedScene);
      setEditingSceneIndex(index !== undefined ? index : null);
    } else {
      setCurrentScene({
        title: '',
        description: '',
        location: '',
        characters: [],
        date: '',
        time: '',
        objetivoEscena: '',
        tono: '',
        puntoDeVista: '',
        conflicto: '',
        notas: '',
        tipoEscena: '',
      });
      setEditingSceneIndex(null);
    }
    setDialogOpen(true);
  };

  const closeDialog = () => {
    setDialogOpen(false);
    setCurrentScene({
      title: '',
      description: '',
      location: '',
      characters: [],
      date: '',
      time: '',
      objetivoEscena: '',
      tono: '',
      puntoDeVista: '',
      conflicto: '',
      notas: '',
      tipoEscena: '',
    });
    setEditingSceneIndex(null);
  };

  const handleSceneInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCurrentScene(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSceneSelectChange = (name: string, value: string) => {
    setCurrentScene(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return {
    dialogOpen,
    editingSceneIndex,
    currentScene,
    setCurrentScene,
    openDialog,
    closeDialog,
    handleSceneInputChange,
    handleSceneSelectChange
  };
};

// Hook para manejar la selección de personajes, localizaciones y puntoDeVista
export const useSelectionHandlers = (characters: Character[], currentScene: OutlineScene, setCurrentScene: React.Dispatch<React.SetStateAction<OutlineScene>>) => {
  const handleCharacterSelect = (characterId: string) => {
    setCurrentScene(prev => {
      const isSelected = prev.characters?.includes(characterId);
      if (isSelected) {
        return {
          ...prev,
          characters: prev.characters?.filter(id => id !== characterId) || []
        };
      } else {
        return {
          ...prev,
          characters: [...(prev.characters || []), characterId]
        };
      }
    });
  };

  const handleLocationSelect = (locationId: string) => {
    setCurrentScene(prev => ({
      ...prev,
      location: locationId
    }));
  };

  const handlePuntoDeVistaSelect = (characterId: string) => {
    setCurrentScene(prev => ({
      ...prev,
      puntoDeVista: characterId
    }));
  };

  const getCharacterName = (characterId: string) => {
    const character = characters.find(c => c.id === characterId);
    return character ? character.name : 'Personaje desconocido';
  };

  return {
    handleCharacterSelect,
    handleLocationSelect,
    handlePuntoDeVistaSelect,
    getCharacterName
  };
};

// Hook para manejar las operaciones de escenas (añadir, editar, eliminar, reordenar)
export const useSceneOperations = (outline: Outline, setOutline: React.Dispatch<React.SetStateAction<Outline>>) => {
  const handleAddScene = (scene: OutlineScene) => {
    setOutline(prev => ({
      ...prev,
      scenes: [...prev.scenes, scene]
    }));
  };

  const handleUpdateScene = (scene: OutlineScene, index: number) => {
    setOutline(prev => {
      const updatedScenes = [...prev.scenes];
      updatedScenes[index] = scene;
      return {
        ...prev,
        scenes: updatedScenes
      };
    });
  };

  const handleDeleteScene = (index: number) => {
    setOutline(prev => {
      const updatedScenes = [...prev.scenes];
      updatedScenes.splice(index, 1);
      return {
        ...prev,
        scenes: updatedScenes
      };
    });
  };

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(outline.scenes);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    setOutline(prev => ({
      ...prev,
      scenes: items
    }));
  };

  return {
    handleAddScene,
    handleUpdateScene,
    handleDeleteScene,
    handleDragEnd
  };
};

// Hook para manejar la persistencia del outline
export const useOutlinePersistence = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [saving, setSaving] = useState(false);

  const saveOutline = async (outline: Outline, isEditMode: boolean) => {
    try {
      setSaving(true);

      // Asegurarse de que todos los campos nuevos estén presentes en cada escena
      const processedScenes = outline.scenes.map(scene => ({
        ...scene,
        objetivoEscena: scene.objetivoEscena || '',
        tono: scene.tono || '',
        puntoDeVista: scene.puntoDeVista || '',
        conflicto: scene.conflicto || '',
        notas: scene.notas || '',
        tipoEscena: scene.tipoEscena || '',
      }));

      const outlineToSave = {
        ...outline,
        scenes: processedScenes
      };

      if (isEditMode && outline.id) {
        await updateOutline(outline.projectId, outline.id, outlineToSave);
        toast({
          title: "Escaleta actualizada",
          description: "La escaleta ha sido actualizada correctamente.",
          duration: 3000,
        });
      } else {
        const newOutline = await createOutline(outline.projectId, outlineToSave);
        toast({
          title: "Escaleta creada",
          description: "La escaleta ha sido creada correctamente.",
          duration: 3000,
        });
        navigate(`/projects/${outline.projectId}/outlines/${newOutline.id}`);
      }

      return true;
    } catch (error) {
      console.error("Error al guardar la escaleta:", error);
      toast({
        title: "Error",
        description: "No se pudo guardar la escaleta. Inténtalo de nuevo.",
        variant: "destructive",
        duration: 3000,
      });
      return false;
    } finally {
      setSaving(false);
    }
  };

  return { saving, saveOutline };
};

// Hook principal que combina los hooks anteriores
interface UseOutlineFormProps {
  projectId: string;
  outlineToEdit?: Outline | null;
}

export const useOutlineForm = ({ projectId, outlineToEdit }: UseOutlineFormProps) => {
  const isEditMode = !!outlineToEdit;

  // Usar los hooks especializados
  const { characters, locations, loadingRelatedData } = useRelatedData(projectId);
  const { outline, setOutline, handleOutlineChange } = useOutlineState(outlineToEdit, projectId);
  const { 
    dialogOpen, 
    editingSceneIndex, 
    currentScene, 
    setCurrentScene,
    openDialog, 
    closeDialog, 
    handleSceneInputChange,
    handleSceneSelectChange 
  } = useSceneDialogState();
  const { 
    handleCharacterSelect, 
    handleLocationSelect, 
    handlePuntoDeVistaSelect,
    getCharacterName 
  } = useSelectionHandlers(characters, currentScene, setCurrentScene);
  const { 
    handleAddScene, 
    handleUpdateScene, 
    handleDeleteScene, 
    handleDragEnd 
  } = useSceneOperations(outline, setOutline);
  const { saving, saveOutline } = useOutlinePersistence();

  // Función para manejar la confirmación del diálogo de escena
  const handleSceneConfirm = () => {
    if (editingSceneIndex !== null) {
      handleUpdateScene(currentScene, editingSceneIndex);
    } else {
      handleAddScene(currentScene);
    }
    closeDialog();
  };

  // Función principal para enviar el formulario
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    return await saveOutline(outline, isEditMode);
  };

  return {
    outline,
    characters,
    locations,
    loadingRelatedData,
    saving,
    dialogOpen,
    currentScene,
    editingSceneIndex,
    handleOutlineChange,
    handleSceneInputChange,
    handleSceneSelectChange,
    handleCharacterSelect,
    handleLocationSelect,
    handlePuntoDeVistaSelect,
    getCharacterName,
    openDialog,
    closeDialog,
    handleSceneConfirm,
    handleDeleteScene,
    handleDragEnd,
    handleSubmit
  };
};