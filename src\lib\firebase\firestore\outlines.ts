import {
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  getDocs,
  getDoc,
  query,
  where,
  serverTimestamp,
  DocumentReference,
  writeBatch
} from "firebase/firestore";
import { db } from "../config";
import { Outline, OutlineWithoutId } from "@/types/outline";

/**
 * Get the outlines subcollection for a project
 */
export const getOutlinesCollection = (projectId: string) => {
  return collection(db, "projects", projectId, "outlines");
};

/**
 * Create a new outline in a project
 */
export const createOutline = async (
  projectId: string,
  outlineData: OutlineWithoutId
): Promise<Outline> => {
  try {
    const outlinesCollection = getOutlinesCollection(projectId);

    const docRef = await addDoc(outlinesCollection, {
      ...outlineData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return {
      id: docRef.id,
      ...outlineData,
      createdAt: serverTimestamp() as any,
      updatedAt: serverTimestamp() as any
    };
  } catch (error) {
    console.error("Error creating outline:", error);
    throw error;
  }
};

/**
 * Get all outlines for a project with fallback to legacy structure
 */
export const getProjectOutlines = async (projectId: string): Promise<Outline[]> => {
  try {
    // First, verify that the project exists and the user has access to it
    const projectRef = doc(db, "projects", projectId);
    const projectSnap = await getDoc(projectRef);

    if (!projectSnap.exists()) {
      throw new Error("Project not found");
    }

    // Store the project data for permission checks
    const projectData = projectSnap.data();
    console.log("Project data for outline permission check:", {
      projectId,
      userId: projectData.userId,
      currentAuth: "Using Firebase Auth"
    });

    // Get outlines from the subcollection structure
    const outlinesCollection = getOutlinesCollection(projectId);
    const querySnapshot = await getDocs(outlinesCollection);

    const outlines = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Outline[];

    console.log(`Retrieved ${outlines.length} outlines from project`);
    return outlines;
  } catch (error) {
    console.error("Error fetching project outlines:", error);
    // Return empty array instead of throwing to prevent app crashes
    return [];
  }
};

/**
 * Get an outline by ID
 */
export const getOutlineById = async (projectId: string, outlineId: string): Promise<Outline> => {
  try {
    // Get the outline from the subcollection structure
    const outlineRef = doc(db, "projects", projectId, "outlines", outlineId);
    const outlineSnap = await getDoc(outlineRef);

    if (outlineSnap.exists()) {
      console.log("Retrieved outline from project subcollection");
      return {
        id: outlineSnap.id,
        ...outlineSnap.data()
      } as Outline;
    } else {
      throw new Error("Outline not found");
    }
  } catch (error) {
    console.error("Error fetching outline:", error);
    throw error;
  }
};

/**
 * Update an outline
 */
export const updateOutline = async (
  projectId: string,
  outlineId: string,
  outlineData: Partial<Outline>
): Promise<Partial<Outline>> => {
  try {
    const updateData = {
      ...outlineData,
      updatedAt: serverTimestamp()
    };

    // Update in subcollection structure
    console.log("Updating outline in project subcollection");
    const outlineRef = doc(db, "projects", projectId, "outlines", outlineId);
    await updateDoc(outlineRef, updateData);

    return {
      id: outlineId,
      ...outlineData
    };
  } catch (error) {
    console.error("Error updating outline:", error);
    throw error;
  }
};

/**
 * Delete an outline
 */
export const deleteOutline = async (projectId: string, outlineId: string): Promise<{ success: boolean }> => {
  try {
    // Delete from the subcollection structure
    const outlineRef = doc(db, "projects", projectId, "outlines", outlineId);
    await deleteDoc(outlineRef);
    console.log("Deleted outline from project subcollection");
    return { success: true };
  } catch (error) {
    console.error("Error deleting outline:", error);
    throw error;
  }
};

/**
 * Delete all outlines for a project (used when deleting a project)
 */
export const deleteAllProjectOutlines = async (projectId: string): Promise<void> => {
  try {
    const outlinesCollection = getOutlinesCollection(projectId);
    const querySnapshot = await getDocs(outlinesCollection);

    const batch = writeBatch(db);

    querySnapshot.docs.forEach(outlineDoc => {
      const outlineRef = doc(db, "projects", projectId, "outlines", outlineDoc.id);
      batch.delete(outlineRef);
    });

    await batch.commit();
  } catch (error) {
    console.error("Error deleting all project outlines:", error);
    throw error;
  }
};

/**
 * Get a reference to an outline document
 */
export const getOutlineRef = (projectId: string, outlineId: string): DocumentReference => {
  return doc(db, "projects", projectId, "outlines", outlineId);
};
