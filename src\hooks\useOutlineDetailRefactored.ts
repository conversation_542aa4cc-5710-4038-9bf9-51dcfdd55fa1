import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { Outline } from '@/types/outline';
import { Project } from '@/types/project';
import { Character } from '@/types/character';
import { getOutlineById, updateOutline, deleteOutline } from '@/lib/firebase/firestore/outlines';
import { getProjectById } from '@/lib/firebase/firestore/projects';
import { getProjectCharacters } from '@/lib/firebase/firestore/characters';
import { useOutlineToScenes } from '@/hooks/useOutlineToScenes';
import { createChapter } from '@/lib/firebase/firestore/chapters';

// Hook para cargar datos del outline y entidades relacionadas
export const useOutlineData = (projectId: string, outlineId: string) => {
  const [outline, setOutline] = useState<Outline | null>(null);
  const [project, setProject] = useState<Project | null>(null);
  const [characters, setCharacters] = useState<Character[]>([]);
  
  // Estados de carga y error más granulares
  const [loadingOutline, setLoadingOutline] = useState(true);
  const [loadingProject, setLoadingProject] = useState(true);
  const [loadingCharacters, setLoadingCharacters] = useState(true);
  
  const [errorOutline, setErrorOutline] = useState<string | null>(null);
  const [errorProject, setErrorProject] = useState<string | null>(null);
  const [errorCharacters, setErrorCharacters] = useState<string | null>(null);

  // Estado de carga general
  const isLoading = loadingOutline || loadingProject || loadingCharacters;
  
  // Verificar si hay algún error
  const hasError = errorOutline !== null || errorProject !== null || errorCharacters !== null;

  useEffect(() => {
    const loadOutline = async () => {
      if (!outlineId || !projectId) {
        setLoadingOutline(false);
        setErrorOutline('IDs de proyecto o escaleta no proporcionados');
        return;
      }

      try {
        setLoadingOutline(true);
        setErrorOutline(null);
        const outlineData = await getOutlineById(projectId, outlineId);
        setOutline(outlineData);
      } catch (error) {
        console.error('Error al cargar la escaleta:', error);
        setErrorOutline('No se pudo cargar la escaleta');
      } finally {
        setLoadingOutline(false);
      }
    };

    const loadProject = async () => {
      if (!projectId) {
        setLoadingProject(false);
        setErrorProject('ID de proyecto no proporcionado');
        return;
      }

      try {
        setLoadingProject(true);
        setErrorProject(null);
        const projectData = await getProjectById(projectId);
        setProject(projectData);
      } catch (error) {
        console.error('Error al cargar el proyecto:', error);
        setErrorProject('No se pudo cargar el proyecto');
      } finally {
        setLoadingProject(false);
      }
    };

    const loadCharacters = async () => {
      if (!projectId) {
        setLoadingCharacters(false);
        setErrorCharacters('ID de proyecto no proporcionado');
        return;
      }

      try {
        setLoadingCharacters(true);
        setErrorCharacters(null);
        const charactersData = await getProjectCharacters(projectId);
        setCharacters(charactersData);
      } catch (error) {
        console.error('Error al cargar los personajes:', error);
        setErrorCharacters('No se pudo cargar los personajes');
      } finally {
        setLoadingCharacters(false);
      }
    };

    // Cargar datos en paralelo
    loadOutline();
    loadProject();
    loadCharacters();
  }, [projectId, outlineId]);

  return {
    outline,
    project,
    characters,
    isLoading,
    hasError,
    loadingOutline,
    loadingProject,
    loadingCharacters,
    errorOutline,
    errorProject,
    errorCharacters,
    setOutline
  };
};

// Hook para manejar las operaciones de actualización y eliminación
export const useOutlineOperations = (projectId: string, outlineId: string) => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleUpdateOutline = async (updatedOutline: Outline) => {
    if (!projectId || !outlineId) return false;

    try {
      setIsUpdating(true);
      await updateOutline(projectId, outlineId, updatedOutline);
      toast({
        title: "Escaleta actualizada",
        description: "La escaleta ha sido actualizada correctamente.",
        duration: 3000,
      });
      return true;
    } catch (error) {
      console.error('Error al actualizar la escaleta:', error);
      toast({
        title: "Error",
        description: "No se pudo actualizar la escaleta. Inténtalo de nuevo.",
        variant: "destructive",
        duration: 3000,
      });
      return false;
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDeleteOutline = async () => {
    if (!projectId || !outlineId) return false;

    try {
      setIsDeleting(true);
      await deleteOutline(projectId, outlineId);
      toast({
        title: "Escaleta eliminada",
        description: "La escaleta ha sido eliminada correctamente.",
        duration: 3000,
      });
      navigate(`/projects/${projectId}`);
      return true;
    } catch (error) {
      console.error('Error al eliminar la escaleta:', error);
      toast({
        title: "Error",
        description: "No se pudo eliminar la escaleta. Inténtalo de nuevo.",
        variant: "destructive",
        duration: 3000,
      });
      return false;
    } finally {
      setIsDeleting(false);
    }
  };

  const handleNavigateBack = () => {
    navigate(`/projects/${projectId}`);
  };

  return {
    isUpdating,
    isDeleting,
    handleUpdateOutline,
    handleDeleteOutline,
    handleNavigateBack
  };
};

// Hook especializado para la conversión de outline a escenas
export const useOutlineConversion = (projectId: string, outline: Outline | null) => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [showConvertDialog, setShowConvertDialog] = useState(false);
  
  // Usar el hook existente para la conversión
  const { isConverting, convertOutlineToScenes } = useOutlineToScenes({ projectId });

  const handleConvertToScenes = async () => {
    if (!outline || !projectId) {
      toast({
        title: "Error",
        description: "No se puede convertir la escaleta sin un proyecto válido.",
        variant: "destructive",
        duration: 3000,
      });
      return;
    }

    try {
      // Crear un nuevo capítulo para las escenas
      const chapterTitle = `Capítulo generado desde ${outline.title}`;
      const chapter = await createChapter(projectId, {
        title: chapterTitle,
        description: `Generado automáticamente desde la escaleta: ${outline.title}`,
        projectId: projectId,
      });

      // Convertir las escenas de la escaleta a escenas de Firestore
      const success = await convertOutlineToScenes(outline, chapter.id);

      if (success) {
        toast({
          title: "Conversión exitosa",
          description: `Las escenas han sido creadas en el capítulo "${chapterTitle}"`,
          duration: 3000,
        });
        navigate(`/projects/${projectId}`);
      }
    } catch (error) {
      console.error('Error en la conversión:', error);
      toast({
        title: "Error en la conversión",
        description: "No se pudieron convertir las escenas. Inténtalo de nuevo.",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  return {
    isConverting,
    showConvertDialog,
    setShowConvertDialog,
    handleConvertToScenes
  };
};

// Hook principal que combina los hooks anteriores
interface UseOutlineDetailProps {
  projectId: string;
  outlineId: string;
}

export const useOutlineDetail = ({ projectId, outlineId }: UseOutlineDetailProps) => {
  // Usar los hooks especializados
  const {
    outline,
    project,
    characters,
    isLoading,
    hasError,
    loadingOutline,
    loadingProject,
    loadingCharacters,
    errorOutline,
    errorProject,
    errorCharacters,
    setOutline
  } = useOutlineData(projectId, outlineId);

  const {
    isUpdating,
    isDeleting,
    handleUpdateOutline,
    handleDeleteOutline,
    handleNavigateBack
  } = useOutlineOperations(projectId, outlineId);

  const {
    isConverting,
    showConvertDialog,
    setShowConvertDialog,
    handleConvertToScenes
  } = useOutlineConversion(projectId, outline);

  // Estado general de carga para la interfaz de usuario
  const isProcessing = isLoading || isUpdating || isDeleting || isConverting;

  return {
    outline,
    project,
    characters,
    isLoading,
    isProcessing,
    hasError,
    loadingOutline,
    loadingProject,
    loadingCharacters,
    errorOutline,
    errorProject,
    errorCharacters,
    isUpdating,
    isDeleting,
    isConverting,
    showConvertDialog,
    setShowConvertDialog,
    handleUpdateOutline,
    handleDeleteOutline,
    handleNavigateBack,
    handleConvertToScenes,
    setOutline
  };
};