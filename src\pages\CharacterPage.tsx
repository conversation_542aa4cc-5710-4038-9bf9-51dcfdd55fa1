
import { useState, useEffect } from 'react';
import { User, Plus, Loader2, Edit, Trash2 } from 'lucide-react';
import CharacterCreator from '@/components/character/CharacterCreator';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { useFirebase } from '@/contexts/FirebaseContext';
import { getUserCharactersFromProjects, deleteCharacter } from '@/lib/firebase/firestore/characters';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useToast } from '@/hooks/use-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Character } from '@/types/firestore-models';
import { useLocation, useNavigate } from 'react-router-dom';
import { Badge } from '@/components/ui/badge';

const CharacterPage = () => {
  const { currentUser } = useFirebase();
  const location = useLocation();
  const navigate = useNavigate();
  const [characters, setCharacters] = useState<Character[]>([]);
  const [loading, setLoading] = useState(true);
  const [characterToEdit, setCharacterToEdit] = useState<Character | null>(null);
  const [characterToDelete, setCharacterToDelete] = useState<Character | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const { toast } = useToast();

  // Determinar la pestaña activa basada en el parámetro de consulta 'tab'
  const queryParams = new URLSearchParams(location.search);
  const tabParam = queryParams.get('tab');
  const [showCreator, setShowCreator] = useState(tabParam !== 'list');

  // Función para cargar personajes
  const loadCharacters = async () => {
    if (currentUser) {
      try {
        setLoading(true);

        // Primero intentamos cargar personajes desde la nueva estructura (subcollections)
        const charactersFromProjects = await getUserCharactersFromProjects(currentUser.uid);

        // Ordenar personajes por fecha de actualización (más recientes primero)
        const sortedCharacters = charactersFromProjects.sort((a, b) => {
          // Si hay timestamps, usarlos para ordenar
          if (a.updatedAt && b.updatedAt) {
            return b.updatedAt.seconds - a.updatedAt.seconds;
          }
          return 0;
        });

        setCharacters(sortedCharacters);
        console.log("Personajes cargados desde proyectos:", sortedCharacters.length);
      } catch (error) {
        console.error("Error al cargar personajes:", error);
      } finally {
        setLoading(false);
      }
    } else {
      setLoading(false);
    }
  };

  // Cargar personajes cuando cambia el usuario
  useEffect(() => {
    loadCharacters();
  }, [currentUser]);

  // Recargar personajes cuando se cambia a la pestaña de lista
  useEffect(() => {
    if (tabParam === 'list') {
      loadCharacters();
    }
  }, [tabParam]);

  const toggleView = (show: boolean) => {
    setShowCreator(show);

    // Actualizar la URL con el parámetro de consulta 'tab'
    const newTab = show ? 'create' : 'list';
    navigate(`/characters?tab=${newTab}`, { replace: true });
  };

  const handleEditCharacter = (character: Character) => {
    setCharacterToEdit(character);
    setIsEditDialogOpen(true);
  };

  const handleDeleteCharacter = (character: Character) => {
    console.log("Eliminando personaje:", character.name, "ID:", character.id, "Proyecto:", character.projectId);
    setCharacterToDelete(character);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteCharacter = async () => {
    if (characterToDelete && characterToDelete.projectId) {
      try {
        setIsDeleting(true);

        // Usar la nueva función de eliminación con projectId y characterId
        await deleteCharacter(characterToDelete.projectId, characterToDelete.id);

        // Actualizar la lista de personajes
        setCharacters(characters.filter(char => char.id !== characterToDelete.id));

        toast({
          title: "Personaje eliminado",
          description: `${characterToDelete.name} ha sido eliminado correctamente.`,
          duration: 3000,
        });
      } catch (error) {
        toast({
          title: "Error al eliminar",
          description: "No se pudo eliminar el personaje. Inténtalo de nuevo.",
          variant: "destructive",
          duration: 3000,
        });
        console.error("Error eliminando personaje:", error);
      } finally {
        setIsDeleting(false);
        setIsDeleteDialogOpen(false);
        setCharacterToDelete(null);
      }
    } else {
      toast({
        title: "Error al eliminar",
        description: "No se pudo identificar el proyecto al que pertenece este personaje.",
        variant: "destructive",
        duration: 3000,
      });
      setIsDeleteDialogOpen(false);
      setCharacterToDelete(null);
    }
  };

  const handleEditDialogClose = () => {
    setIsEditDialogOpen(false);
    setCharacterToEdit(null);
  };

  const handleCharacterUpdated = (updatedCharacter: Character) => {
    setCharacters(prevCharacters =>
      prevCharacters.map(char =>
        char.id === updatedCharacter.id ? updatedCharacter : char
      )
    );
    setIsEditDialogOpen(false);
    setCharacterToEdit(null);
    toast({
      title: "Personaje actualizado",
      description: `${updatedCharacter.name} ha sido actualizado correctamente.`,
      duration: 3000,
    });
  };

  // Actualizar showCreator cuando cambia el parámetro de consulta 'tab'
  useEffect(() => {
    setShowCreator(tabParam !== 'list');
  }, [tabParam]);

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="text-center mb-10">
        <h1 className="font-heading text-3xl font-bold mb-2 gradient-text inline-block">
          Creador de Personajes
        </h1>
        <p className="text-muted-foreground max-w-xl mx-auto">
          Desarrolla personajes complejos y memorables para tus historias con esta herramienta interactiva.
        </p>
        <p className="text-sm text-muted-foreground mt-2">
          Nota: Para una mejor experiencia, crea y gestiona tus personajes desde un proyecto específico.
        </p>
      </div>

      {currentUser ? (
        <div className="max-w-3xl mx-auto">
          <Tabs value={showCreator ? "create" : "list"} onValueChange={(value) => toggleView(value === "create")}>
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="create">Crear Personaje</TabsTrigger>
              <TabsTrigger value="list">Mis Personajes {characters.length > 0 && `(${characters.length})`}</TabsTrigger>
            </TabsList>

            <TabsContent value="create">
              <CharacterCreator />
            </TabsContent>

            <TabsContent value="list">
              {loading ? (
                <div className="flex justify-center items-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  <span className="ml-2">Cargando personajes...</span>
                </div>
              ) : characters.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {characters.map((character) => (
                    <Card key={character.id} className="overflow-hidden hover:shadow-md transition-shadow flex flex-col">
                      <CardHeader
                        className="bg-secondary/30"
                        style={character.color_tag ? { borderTop: `4px solid ${character.color_tag}` } : undefined}
                      >
                        <div className="flex justify-between items-start">
                          <CardTitle className="text-lg">{character.name}</CardTitle>
                          {character.role && (
                            <Badge variant="outline">{character.role}</Badge>
                          )}
                        </div>
                        {character.projectTitle && (
                          <div className="text-xs text-muted-foreground mt-1">
                            Proyecto: {character.projectTitle}
                          </div>
                        )}
                      </CardHeader>
                      <CardContent className="pt-4 flex-grow">
                        <div className="space-y-2">
                          {/* Mostrar alias si existen */}
                          {character.alias && character.alias.length > 0 && (
                            <div className="mb-2">
                              <span className="text-xs text-muted-foreground">También conocido como:</span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {character.alias.map((alias, index) => (
                                  <Badge key={index} variant="secondary" className="text-xs">
                                    {alias}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Mostrar rasgos distintivos si existen */}
                          {character.traits && character.traits.length > 0 && (
                            <div className="mb-2">
                              <span className="text-xs text-muted-foreground">Rasgos distintivos:</span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {character.traits.map((trait, index) => (
                                  <Badge
                                    key={index}
                                    style={character.color_tag ? { backgroundColor: character.color_tag, color: '#fff' } : undefined}
                                    className="text-xs"
                                  >
                                    {trait}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Información básica */}
                          <div className="grid grid-cols-2 gap-2">
                            {character.age && (
                              <p className="text-sm"><span className="font-medium">Edad:</span> {character.age}</p>
                            )}
                            {character.occupation && (
                              <p className="text-sm"><span className="font-medium">Ocupación:</span> {character.occupation}</p>
                            )}
                          </div>

                          {/* Atributos */}
                          {(character.strength || character.intelligence || character.charisma || character.resilience) && (
                            <div className="grid grid-cols-2 gap-2 mt-3">
                              {character.strength && (
                                <div className="text-xs bg-secondary/20 p-2 rounded-md">
                                  <div className="flex justify-between">
                                    <span>Fuerza:</span>
                                    <span className="font-semibold">{character.strength}%</span>
                                  </div>
                                  <div className="w-full bg-secondary/40 rounded-full h-1.5 mt-1">
                                    <div className="bg-red-500 h-1.5 rounded-full" style={{ width: `${character.strength}%` }}></div>
                                  </div>
                                </div>
                              )}
                              {character.intelligence && (
                                <div className="text-xs bg-secondary/20 p-2 rounded-md">
                                  <div className="flex justify-between">
                                    <span>Inteligencia:</span>
                                    <span className="font-semibold">{character.intelligence}%</span>
                                  </div>
                                  <div className="w-full bg-secondary/40 rounded-full h-1.5 mt-1">
                                    <div className="bg-blue-500 h-1.5 rounded-full" style={{ width: `${character.intelligence}%` }}></div>
                                  </div>
                                </div>
                              )}
                              {character.charisma && (
                                <div className="text-xs bg-secondary/20 p-2 rounded-md">
                                  <div className="flex justify-between">
                                    <span>Carisma:</span>
                                    <span className="font-semibold">{character.charisma}%</span>
                                  </div>
                                  <div className="w-full bg-secondary/40 rounded-full h-1.5 mt-1">
                                    <div className="bg-pink-500 h-1.5 rounded-full" style={{ width: `${character.charisma}%` }}></div>
                                  </div>
                                </div>
                              )}
                              {character.resilience && (
                                <div className="text-xs bg-secondary/20 p-2 rounded-md">
                                  <div className="flex justify-between">
                                    <span>Resiliencia:</span>
                                    <span className="font-semibold">{character.resilience}%</span>
                                  </div>
                                  <div className="w-full bg-secondary/40 rounded-full h-1.5 mt-1">
                                    <div className="bg-amber-500 h-1.5 rounded-full" style={{ width: `${character.resilience}%` }}></div>
                                  </div>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </CardContent>
                      <CardFooter className="flex justify-end gap-2 bg-secondary/10 p-3 mt-auto">
                        <div className="flex gap-2 w-full justify-end">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEditCharacter(character)}
                            className="flex items-center"
                          >
                            <Edit className="h-4 w-4 mr-1" /> Editar
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleDeleteCharacter(character)}
                            className="flex items-center"
                          >
                            <Trash2 className="h-4 w-4 mr-1" /> Eliminar
                          </Button>
                        </div>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 bg-secondary/10 rounded-lg border border-dashed border-secondary">
                  <p className="text-muted-foreground mb-4">No has creado ningún personaje todavía.</p>
                  <Button variant="default" onClick={() => toggleView(true)}>
                    <Plus className="mr-2 h-4 w-4" />
                    Crear mi primer personaje
                  </Button>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      ) : (
        <div className="max-w-3xl mx-auto">
          <CharacterCreator />
        </div>
      )}

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>¿Estás seguro?</AlertDialogTitle>
            <AlertDialogDescription>
              Esta acción no se puede deshacer. El personaje {characterToDelete?.name} será eliminado permanentemente.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteCharacter}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Eliminando...
                </>
              ) : (
                "Eliminar"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Editar Personaje: {characterToEdit?.name}</DialogTitle>
          </DialogHeader>
          {characterToEdit && (
            <CharacterCreator
              editMode={true}
              characterToEdit={characterToEdit}
              onCharacterUpdated={handleCharacterUpdated}
              onCancel={handleEditDialogClose}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CharacterPage;

