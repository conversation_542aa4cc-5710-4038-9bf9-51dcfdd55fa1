import { DocumentReference } from "firebase/firestore";
import { OutlineScene } from "@/types/outline";
import { Scene, SceneWithoutId } from "@/types/firestore-models";
import { getCharacterRef } from "./characters";
import { createScene } from "./scenes";

/**
 * Convierte el ID del personaje (puntoDeVista) en una referencia de documento (punto_de_vista)
 */
export const convertPuntoDeVistaToReference = (projectId: string, characterId?: string): DocumentReference | undefined => {
  if (!characterId) return undefined;
  return getCharacterRef(projectId, characterId);
};

/**
 * Convierte una escena de outline (OutlineScene) en una escena de Firestore (SceneWithoutId)
 */
export const createSceneDataFromOutlineScene = (
  projectId: string,
  outlineScene: OutlineScene,
  chapterId: DocumentReference
): SceneWithoutId => {
  // Crear datos básicos de la escena
  const sceneData: SceneWithoutId = {
    title: outlineScene.title,
    summary: outlineScene.description,
    chapter_id: chapterId,
    global_order: outlineScene.order,
    status: "Por escribir",
    word_count: 0,
    characters_involved: [],
    // Convertir puntoDeVista a punto_de_vista
    punto_de_vista: convertPuntoDeVistaToReference(projectId, outlineScene.puntoDeVista),
  };

  // Añadir campos adicionales si están disponibles
  if (outlineScene.sceneDate) {
    sceneData.fecha_escena = outlineScene.sceneDate.toISOString().split('T')[0];
  }

  if (outlineScene.sceneTime) {
    sceneData.hora_escena = outlineScene.sceneTime;
  }

  if (outlineScene.objetivoEscena) {
    sceneData.objetivo_escena = outlineScene.objetivoEscena;
  }

  // Añadir contenido del editor de texto enriquecido
  if (outlineScene.content) {
    sceneData.content = outlineScene.content;
  }

  return sceneData;
};

/**
 * Crea escenas en Firestore a partir de las escenas de un outline
 */
export const createScenesFromOutline = async (
  projectId: string,
  outlineScenes: OutlineScene[],
  chapterId: DocumentReference
): Promise<Scene[]> => {
  try {
    const createdScenes: Scene[] = [];
    
    // Crear cada escena en Firestore
    for (let i = 0; i < outlineScenes.length; i++) {
      const outlineScene = outlineScenes[i];
      const sceneData = createSceneDataFromOutlineScene(projectId, outlineScene, chapterId);
      
      // Asegurar que el orden global se establece correctamente
      sceneData.global_order = i;
      
      // Crear la escena en Firestore
      const createdScene = await createScene(projectId, sceneData);
      createdScenes.push(createdScene);
    }
    
    return createdScenes;
  } catch (error) {
    console.error("Error creating scenes from outline:", error);
    throw error;
  }
};