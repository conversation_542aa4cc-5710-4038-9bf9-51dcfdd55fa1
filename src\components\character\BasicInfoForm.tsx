
import ProfilePictureUploader from './ProfilePictureUploader';
import { TextField, TextAreaField, GeneratableTextField, DatePickerField } from './FormFields';
import { Character } from '@/types/firestore-models';
import ArrayField from './ArrayField';
import ColorPicker from './ColorPicker';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface BasicInfoFormProps {
  character: Character | Omit<Character, 'id'>;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onSelectChange: (name: string, value: string) => void;
  onDateChange: (date: Date | undefined) => void;
  onFileChange: (url: string) => void;
  onColorChange: (color: string) => void;
  onAddToArray: (name: string, value: string) => void;
  onRemoveFromArray: (name: string, value: string) => void;
  onGenerateRandomName?: () => void;
  onGenerateRandomOccupation?: () => void;
}

const characterRoles = [
  'Protagonista',
  'Antagonista',
  'Secundario',
  'Mentor',
  'Aliado',
  'Interés romántico',
  'Confidente',
  'Guardián',
  'Mensajero',
  'Figura de cambio',
  'Escéptico',
  'Tentador',
  'Sirviente',
  'Otro'
];

const BasicInfoForm = ({
  character,
  onInputChange,
  onSelectChange,
  onDateChange,
  onFileChange,
  onColorChange,
  onAddToArray,
  onRemoveFromArray,
  onGenerateRandomName,
  onGenerateRandomOccupation
}: BasicInfoFormProps) => {
  return (
    <div className="space-y-4">
      {/* Profile Image and Basic Info */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="md:col-span-1">
          <ProfilePictureUploader
            name={character.name}
            profilePicture={character.image_url || character.profilePicture}
            onFileChange={onFileChange}
          />
        </div>

        <div className="md:col-span-2 space-y-4">
          <GeneratableTextField
            id="name"
            name="name"
            label="Nombre"
            value={character.name}
            onChange={onInputChange}
            placeholder="Nombre del personaje"
            onGenerate={onGenerateRandomName}
            generateTooltip="Generar nombre aleatorio"
          />

          <ArrayField
            id="alias"
            name="alias"
            label="Alias / Apodos"
            values={character.alias || []}
            onAddItem={onAddToArray}
            onRemoveItem={onRemoveFromArray}
            placeholder="Añadir alias o apodo..."
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Rol en la historia</label>
              <Select
                value={character.role || ''}
                onValueChange={(value) => onSelectChange('role', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar rol" />
                </SelectTrigger>
                <SelectContent>
                  {characterRoles.map((role) => (
                    <SelectItem key={role} value={role}>
                      {role}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <ColorPicker
              id="color_tag"
              label="Color identificativo"
              value={character.color_tag || '#6366F1'}
              onChange={onColorChange}
            />
          </div>
        </div>
      </div>

      {/* Character Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <DatePickerField
          id="fecha_nacimiento"
          label="Fecha de Nacimiento"
          value={character.fecha_nacimiento instanceof Date ? character.fecha_nacimiento : undefined}
          onChange={onDateChange}
          placeholder="Seleccionar fecha"
        />

        <GeneratableTextField
          id="occupation"
          name="occupation"
          label="Ocupación"
          value={character.occupation || ''}
          onChange={onInputChange}
          placeholder="Ocupación del personaje"
          onGenerate={onGenerateRandomOccupation}
          generateTooltip="Generar ocupación aleatoria"
        />
      </div>

      {/* Character Traits */}
      <ArrayField
        id="traits"
        name="traits"
        label="Rasgos distintivos"
        values={character.traits || []}
        onAddItem={onAddToArray}
        onRemoveItem={onRemoveFromArray}
        placeholder="Añadir rasgo distintivo..."
        badgeColor={character.color_tag}
      />

      <ArrayField
        id="personalidad"
        name="personalidad"
        label="Palabras clave de personalidad"
        values={character.personalidad || []}
        onAddItem={onAddToArray}
        onRemoveItem={onRemoveFromArray}
        placeholder="Añadir rasgo de personalidad..."
      />

      {/* Text areas section */}
      <div className="space-y-4">
        <TextAreaField
          id="personality"
          name="personality"
          label="Descripción de personalidad"
          value={character.personality || ''}
          onChange={onInputChange}
          placeholder="Describe en detalle la personalidad de tu personaje..."
          minHeight="100px"
        />

        <TextAreaField
          id="descripcion_fisica"
          name="descripcion_fisica"
          label="Descripción Física"
          value={character.descripcion_fisica || character.physicalDescription || ''}
          onChange={onInputChange}
          placeholder="Describe la apariencia física de tu personaje..."
          minHeight="80px"
        />

        <TextAreaField
          id="bio"
          name="bio"
          label="Historia de fondo"
          value={character.bio || character.background || ''}
          onChange={onInputChange}
          placeholder="Cuenta la historia y antecedentes de tu personaje..."
          minHeight="100px"
        />

        <TextAreaField
          id="motivacion"
          name="motivacion"
          label="Motivación / Objetivo principal"
          value={character.motivacion || character.motivation || ''}
          onChange={onInputChange}
          placeholder="¿Qué motiva a tu personaje? ¿Cuáles son sus objetivos principales?"
          minHeight="80px"
        />

        <TextAreaField
          id="notas_adicionales"
          name="notas_adicionales"
          label="Notas adicionales"
          value={character.notas_adicionales || ''}
          onChange={onInputChange}
          placeholder="Información adicional relevante sobre el personaje..."
          minHeight="80px"
        />
      </div>
    </div>
  );
};

export default BasicInfoForm;
