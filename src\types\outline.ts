import { Timestamp } from "firebase/firestore";

export interface OutlineScene {
  id: string;
  title: string;
  description: string;
  characters?: string[];
  location?: string;
  order: number;
  sceneDate?: Date;
  sceneTime?: string;
  objetivoEscena?: string;
  tono?: string;
  elementoSimbolico?: string;
  desarrolloEmocional?: string;
  funcionHistoria?: string;
  puntoDeVista?: string; // ID del personaje que narra la escena
}

export interface Outline {
  id: string;
  title: string;
  description?: string;
  projectId: string;
  userId: string;
  scenes: OutlineScene[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export type OutlineWithoutId = Omit<Outline, 'id' | 'createdAt' | 'updatedAt'>;

export type OutlineSceneWithoutId = Omit<OutlineScene, 'id'>;
