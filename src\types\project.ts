
import { Character } from './character';

export interface Project {
  id: string;
  title: string;
  description: string;
  genre?: string;
  coverImage?: string;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  characters?: Character[];
  // Futuros módulos
  // documentation?: Documentation[];
  // outline?: Outline[];
  // chapters?: Chapter[];
  // writingAssistant?: WritingAssistant[];
}

export type ProjectWithoutId = Omit<Project, 'id'>;
