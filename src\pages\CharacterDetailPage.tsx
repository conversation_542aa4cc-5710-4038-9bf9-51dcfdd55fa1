
import { useParams, useNavigate } from 'react-router-dom';
import { useState } from 'react';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import CharacterCreator from '@/components/character/CharacterCreator';
import LoadingState from '@/components/project/LoadingState';
import ProjectNotFound from '@/components/project/ProjectNotFound';
import CharacterProfile from '@/components/character/CharacterProfile';
import CharacterAttributes from '@/components/character/CharacterAttributes';
import CharacterDetails from '@/components/character/CharacterDetails';
import { useCharacterDetail } from '@/hooks/useCharacterDetail';
import { Character } from '@/types/firestore-models';

const CharacterDetailPage = () => {
  const { projectId, characterId } = useParams<{ projectId: string, characterId: string }>();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [editMode, setEditMode] = useState(false);

  const { character, project, isLoading, setCharacter } = useCharacterDetail(projectId, characterId);

  const handleCharacterUpdated = (updatedCharacter: Character) => {
    setCharacter(updatedCharacter);
    setEditMode(false);
    toast({
      title: "Personaje actualizado",
      description: "Los cambios han sido guardados correctamente",
    });
  };

  const handleBackToProject = () => {
    if (project) {
      navigate(`/projects/${project.id}`);
    } else {
      navigate('/projects');
    }
  };

  if (isLoading) {
    return <LoadingState />;
  }

  if (!character || !project) {
    return <ProjectNotFound />;
  }

  if (editMode) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Button
          variant="outline"
          className="mb-4"
          onClick={() => setEditMode(false)}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Cancelar edición
        </Button>

        <CharacterCreator
          editMode={true}
          characterToEdit={character}
          onCharacterUpdated={handleCharacterUpdated}
          onCancel={() => setEditMode(false)}
          projectId={projectId}
        />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <Button
        variant="outline"
        className="mb-4"
        onClick={handleBackToProject}
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Volver a {project.title}
      </Button>

      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">{character.name}</h1>
        <Button onClick={() => setEditMode(true)}>
          Editar personaje
        </Button>
      </div>

      <Separator className="mb-6" />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <CharacterProfile
            name={character.name}
            occupation={character.occupation}
            age={character.age}
            profilePicture={character.profilePicture}
            image_url={character.image_url}
            role={character.role}
            color_tag={character.color_tag}
            alias={character.alias}
          />
          <CharacterAttributes
            strength={character.strength || 0}
            intelligence={character.intelligence || 0}
            charisma={character.charisma || 0}
            resilience={character.resilience || 0}
          />
        </div>

        <div className="col-span-2">
          <CharacterDetails
            // Legacy fields
            personality={character.personality}
            physicalDescription={character.physicalDescription}
            background={character.background}
            motivation={character.motivation}

            // New fields
            personalidad={character.personalidad}
            descripcion_fisica={character.descripcion_fisica}
            bio={character.bio}
            motivacion={character.motivacion}
            traits={character.traits}
            internal_conflict={character.internal_conflict}
            external_conflict={character.external_conflict}
            arc_summary={character.arc_summary}
            initial_state={character.initial_state}
            final_state={character.final_state}
            notas_adicionales={character.notas_adicionales}
            color_tag={character.color_tag}
          />
        </div>
      </div>
    </div>
  );
};

export default CharacterDetailPage;
