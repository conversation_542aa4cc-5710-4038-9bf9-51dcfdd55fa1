
import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON>r, <PERSON>ader2, Brain, HeartHandshake, Sparkles } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Character } from '@/types/firestore-models';
import { useCharacterForm } from '@/hooks/useCharacterFormRefactored';
import BasicInfoForm from './BasicInfoForm';
import AttributesForm from './AttributesForm';
import DevelopmentForm from './DevelopmentForm';
import ConflictsForm from './ConflictsForm';
import CharacterActions from './CharacterActions';

// Sample first names and last names for the name generator
const firstNames = [
  "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>",
  "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"
];

const lastNames = [
  "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
  "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"
];

const occupations = [
  "Profesor/a", "<PERSON>édico/a", "Artista", "Ingeniero/a", "Esc<PERSON>r/a",
  "<PERSON>", "<PERSON>ient<PERSON>fico/a", "Abogado/a", "M<PERSON>ico", "Chef",
  "<PERSON>ista", "Atleta", "Arquitecto/a", "Empresario/a", "Investigador/a"
];

interface CharacterCreatorProps {
  editMode?: boolean;
  characterToEdit?: Character;
  onCharacterUpdated?: (character: Character) => void;
  onCancel?: () => void;
  projectId?: string;
}

const CharacterCreator = ({
  editMode = false,
  characterToEdit,
  onCharacterUpdated,
  onCancel,
  projectId
}: CharacterCreatorProps) => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("basic");

  const {
    character,
    saving,
    handleInputChange,
    handleSelectChange,
    handleDateChange,
    handleImageChange,
    handleColorChange,
    handleArrayChange,
    handleAddToArray,
    handleRemoveFromArray,
    handleSliderChange,
    saveCharacterToFirestore,
    resetForm
  } = useCharacterForm({
    editMode,
    characterToEdit,
    onCharacterUpdated,
    projectId
  });

  const generateRandomName = () => {
    const randomFirstName = firstNames[Math.floor(Math.random() * firstNames.length)];
    const randomLastName = lastNames[Math.floor(Math.random() * lastNames.length)];

    const nameEvent = {
      target: {
        name: 'name',
        value: `${randomFirstName} ${randomLastName}`
      }
    } as React.ChangeEvent<HTMLInputElement>;

    handleInputChange(nameEvent);
  };

  const generateRandomOccupation = () => {
    const randomOccupation = occupations[Math.floor(Math.random() * occupations.length)];
    handleSelectChange('occupation', randomOccupation);
  };

  const handleSave = async () => {
    const success = await saveCharacterToFirestore();

    if (success) {
      if (editMode) {
        // Si estamos en modo edición, llamamos a onCancel para cerrar el formulario
        onCancel?.();
      } else {
        // Si estamos creando un nuevo personaje, redirigimos a la pestaña de personajes
        // Solo redirigimos si no estamos en un proyecto específico (estamos en la página de personajes)
        if (!projectId || projectId === 'default') {
          // Cambiamos a la pestaña de "Mis Personajes"
          navigate('/characters?tab=list');
        } else {
          // Si estamos en un proyecto, simplemente cerramos el formulario
          onCancel?.();
        }
      }
    }
  };

  return (
    <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid w-full grid-cols-4">
        <TabsTrigger value="basic" className="flex items-center gap-1">
          <User className="h-4 w-4" />
          <span className="hidden md:inline">Información Básica</span>
          <span className="inline md:hidden">Básica</span>
        </TabsTrigger>
        <TabsTrigger value="attributes" className="flex items-center gap-1">
          <Brain className="h-4 w-4" />
          <span className="hidden md:inline">Atributos</span>
          <span className="inline md:hidden">Atributos</span>
        </TabsTrigger>
        <TabsTrigger value="conflicts" className="flex items-center gap-1">
          <HeartHandshake className="h-4 w-4" />
          <span className="hidden md:inline">Conflictos</span>
          <span className="inline md:hidden">Conflictos</span>
        </TabsTrigger>
        <TabsTrigger value="development" className="flex items-center gap-1">
          <Sparkles className="h-4 w-4" />
          <span className="hidden md:inline">Desarrollo</span>
          <span className="inline md:hidden">Desarrollo</span>
        </TabsTrigger>
      </TabsList>

      <TabsContent value="basic" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="mr-2 h-5 w-5 text-primary" />
              Perfil del Personaje
            </CardTitle>
            <CardDescription>
              {editMode ? "Edita el perfil de tu personaje" : "Crea un perfil detallado de tu personaje"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <BasicInfoForm
              character={character}
              onInputChange={handleInputChange}
              onSelectChange={handleSelectChange}
              onDateChange={handleDateChange}
              onFileChange={handleImageChange}
              onColorChange={handleColorChange}
              onAddToArray={handleAddToArray}
              onRemoveFromArray={handleRemoveFromArray}
              onGenerateRandomName={generateRandomName}
              onGenerateRandomOccupation={generateRandomOccupation}
            />
          </CardContent>
          <CardFooter>
            {editMode ? (
              <div className="flex gap-2 w-full">
                <Button variant="outline" onClick={onCancel} className="flex-1">
                  Cancelar
                </Button>
                <Button onClick={handleSave} disabled={saving} className="flex-1">
                  {saving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Guardando...
                    </>
                  ) : (
                    <>
                      <User className="mr-2 h-4 w-4" />
                      Actualizar Personaje
                    </>
                  )}
                </Button>
              </div>
            ) : (
              <CharacterActions
                onSave={handleSave}
                onReset={resetForm}
                saving={saving}
              />
            )}
          </CardFooter>
        </Card>
      </TabsContent>

      <TabsContent value="attributes" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Atributos del Personaje</CardTitle>
            <CardDescription>
              Define las características y habilidades de tu personaje
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <AttributesForm
              character={character}
              onSliderChange={handleSliderChange}
            />
          </CardContent>
          <CardFooter>
            {editMode ? (
              <div className="flex gap-2 w-full">
                <Button variant="outline" onClick={onCancel} className="flex-1">
                  Cancelar
                </Button>
                <Button onClick={handleSave} disabled={saving} className="flex-1">
                  {saving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Guardando...
                    </>
                  ) : (
                    <>
                      <User className="mr-2 h-4 w-4" />
                      Actualizar Personaje
                    </>
                  )}
                </Button>
              </div>
            ) : (
              <Button onClick={handleSave} disabled={saving} className="w-full">
                {saving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Guardando...
                  </>
                ) : (
                  <>
                    <User className="mr-2 h-4 w-4" />
                    Guardar Personaje
                  </>
                )}
              </Button>
            )}
          </CardFooter>
        </Card>
      </TabsContent>

      <TabsContent value="conflicts" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <HeartHandshake className="mr-2 h-5 w-5 text-primary" />
              Conflictos del Personaje
            </CardTitle>
            <CardDescription>
              Define los conflictos internos y externos que enfrenta tu personaje
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <ConflictsForm
              character={character}
              onInputChange={handleInputChange}
            />
          </CardContent>
          <CardFooter>
            {editMode ? (
              <div className="flex gap-2 w-full">
                <Button variant="outline" onClick={onCancel} className="flex-1">
                  Cancelar
                </Button>
                <Button onClick={handleSave} disabled={saving} className="flex-1">
                  {saving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Guardando...
                    </>
                  ) : (
                    <>
                      <User className="mr-2 h-4 w-4" />
                      Actualizar Personaje
                    </>
                  )}
                </Button>
              </div>
            ) : (
              <Button onClick={handleSave} disabled={saving} className="w-full">
                {saving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Guardando...
                  </>
                ) : (
                  <>
                    <User className="mr-2 h-4 w-4" />
                    Guardar Personaje
                  </>
                )}
              </Button>
            )}
          </CardFooter>
        </Card>
      </TabsContent>

      <TabsContent value="development" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Sparkles className="mr-2 h-5 w-5 text-primary" />
              Desarrollo del Personaje
            </CardTitle>
            <CardDescription>
              Define cómo evoluciona tu personaje a lo largo de la historia
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <DevelopmentForm
              character={character}
              onInputChange={handleInputChange}
            />
          </CardContent>
          <CardFooter>
            {editMode ? (
              <div className="flex gap-2 w-full">
                <Button variant="outline" onClick={onCancel} className="flex-1">
                  Cancelar
                </Button>
                <Button onClick={handleSave} disabled={saving} className="flex-1">
                  {saving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Guardando...
                    </>
                  ) : (
                    <>
                      <User className="mr-2 h-4 w-4" />
                      Actualizar Personaje
                    </>
                  )}
                </Button>
              </div>
            ) : (
              <Button onClick={handleSave} disabled={saving} className="w-full">
                {saving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Guardando...
                  </>
                ) : (
                  <>
                    <User className="mr-2 h-4 w-4" />
                    Guardar Personaje
                  </>
                )}
              </Button>
            )}
          </CardFooter>
        </Card>
      </TabsContent>
    </Tabs>
  );
};

export default CharacterCreator;
