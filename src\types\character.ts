
import { DocumentReference, Timestamp } from "firebase/firestore";

export interface Character {
  id: string;
  name: string;
  alias?: string[];
  image_url?: string;
  color_tag?: string;
  role?: string;
  descripcion_fisica?: string;
  personalidad?: string[];
  traits?: string[];
  motivacion?: string;
  internal_conflict?: string;
  external_conflict?: string;
  bio?: string;
  arc_summary?: string;
  initial_state?: string;
  final_state?: string;
  fecha_nacimiento?: Timestamp | string | Date;
  notas_adicionales?: string;
  projectId: string; // ID del proyecto al que pertenece este personaje

  // Legacy fields for backward compatibility
  age?: string;
  occupation?: string;
  physicalDescription?: string;
  personality?: string;
  background?: string;
  motivation?: string;
  strength?: number;
  intelligence?: number;
  charisma?: number;
  resilience?: number;
  profilePicture?: string;
  [key: string]: any;
}

export interface CharacterRelationship {
  id: string;
  target_character_ref: DocumentReference;
  relationship_type: string;
  description: string;
  status?: string;
}
