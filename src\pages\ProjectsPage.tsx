
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, Plus, BookOpen, Trash, Edit, AlertTriangle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useFirebase } from '@/contexts/FirebaseContext';
import { getUserProjects, deleteProjectLegacy as deleteProject } from '@/lib/firebase';
import { createProject } from '@/lib/firebase/firestore/projects';
import { Project } from '@/types/firestore-models';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';

const ProjectsPage = () => {
  const { toast } = useToast();
  const { currentUser } = useFirebase();
  const navigate = useNavigate();
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [newProject, setNewProject] = useState({
    title: '',
    logline: '',
    theme: '',
    genre: '',
    target_audience: '',
    overall_status: 'Idea'
  });
  const [openDialog, setOpenDialog] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<string | null>(null);

  useEffect(() => {
    const loadProjects = async () => {
      if (!currentUser) {
        navigate('/auth');
        return;
      }

      try {
        setIsLoading(true);
        const userProjects = await getUserProjects(currentUser.uid);
        setProjects(userProjects);
      } catch (error) {
        console.error("Error loading projects:", error);
        toast({
          title: "Error al cargar proyectos",
          description: "No se pudieron cargar tus proyectos. Por favor, intenta de nuevo.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadProjects();
  }, [currentUser, navigate, toast]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNewProject(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (value: string, name: string) => {
    setNewProject(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCreateProject = async () => {
    if (!currentUser) {
      toast({
        title: "Inicio de sesión requerido",
        description: "Debes iniciar sesión para crear proyectos.",
        variant: "destructive",
      });
      navigate('/auth');
      return;
    }

    if (!newProject.title.trim()) {
      toast({
        title: "Título requerido",
        description: "Por favor, añade un título a tu proyecto.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsCreating(true);
      const projectData = {
        title: newProject.title,
        logline: newProject.logline,
        theme: newProject.theme,
        genre: newProject.genre,
        target_audience: newProject.target_audience,
        overall_status: newProject.overall_status,
        userId: currentUser.uid,
      };

      const createdProject = await createProject(projectData);

      setProjects(prev => [...prev, createdProject]);
      setNewProject({
        title: '',
        logline: '',
        theme: '',
        genre: '',
        target_audience: '',
        overall_status: 'Idea'
      });
      setOpenDialog(false);

      toast({
        title: "Proyecto creado",
        description: `"${newProject.title}" ha sido creado correctamente.`,
      });
    } catch (error) {
      console.error("Error creating project:", error);
      toast({
        title: "Error al crear proyecto",
        description: "No se pudo crear el proyecto. Por favor, intenta de nuevo.",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleDeleteProject = async (projectId: string) => {
    try {
      setIsDeleting(true);
      await deleteProject(projectId);

      setProjects(prev => prev.filter(project => project.id !== projectId));

      toast({
        title: "Proyecto eliminado",
        description: "El proyecto ha sido eliminado correctamente.",
      });
    } catch (error) {
      console.error("Error deleting project:", error);
      toast({
        title: "Error al eliminar proyecto",
        description: "No se pudo eliminar el proyecto. Por favor, intenta de nuevo.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
      setProjectToDelete(null);
    }
  };

  const navigateToProject = (projectId: string) => {
    navigate(`/projects/${projectId}`);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 px-4 flex flex-col items-center justify-center min-h-[60vh]">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Cargando proyectos...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="font-heading text-3xl font-bold mb-2 gradient-text inline-block">
            Mis Proyectos
          </h1>
          <p className="text-muted-foreground">
            Gestiona tus proyectos de escritura y accede a todos tus módulos creativos.
          </p>
        </div>

        <Dialog open={openDialog} onOpenChange={setOpenDialog}>
          <DialogTrigger asChild>
            <Button className="mt-4 md:mt-0">
              <Plus className="h-4 w-4 mr-2" />
              Nuevo Proyecto
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Crear nuevo proyecto</DialogTitle>
              <DialogDescription>
                Crea un proyecto para organizar tus personajes, capítulos, escaletas y más.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <label htmlFor="title" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  Título
                </label>
                <Input
                  id="title"
                  name="title"
                  value={newProject.title}
                  onChange={handleInputChange}
                  placeholder="Escribe el título de tu proyecto"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="logline" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  Premisa/Sinopsis breve
                </label>
                <Textarea
                  id="logline"
                  name="logline"
                  value={newProject.logline}
                  onChange={handleInputChange}
                  placeholder="Breve descripción de la historia y su concepto principal"
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="theme" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  Tema
                </label>
                <Input
                  id="theme"
                  name="theme"
                  value={newProject.theme}
                  onChange={handleInputChange}
                  placeholder="Tema principal de la obra"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="genre" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  Género
                </label>
                <Input
                  id="genre"
                  name="genre"
                  value={newProject.genre}
                  onChange={handleInputChange}
                  placeholder="Novela, Cuento, Ensayo, etc."
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="target_audience" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Audiencia objetivo
                  </label>
                  <Input
                    id="target_audience"
                    name="target_audience"
                    value={newProject.target_audience}
                    onChange={handleInputChange}
                    placeholder="Adulto, Juvenil, Infantil, etc."
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="overall_status" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Estado
                  </label>
                  <Select
                    value={newProject.overall_status}
                    onValueChange={(value) => handleSelectChange(value, 'overall_status')}
                  >
                    <SelectTrigger id="overall_status">
                      <SelectValue placeholder="Selecciona el estado del proyecto" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Idea">Idea</SelectItem>
                      <SelectItem value="Borrador 1">Borrador 1</SelectItem>
                      <SelectItem value="Borrador 2">Borrador 2</SelectItem>
                      <SelectItem value="Borrador 3">Borrador 3</SelectItem>
                      <SelectItem value="Editando">Editando</SelectItem>
                      <SelectItem value="Finalizado">Finalizado</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setOpenDialog(false)}>Cancelar</Button>
              <Button onClick={handleCreateProject} disabled={isCreating}>
                {isCreating ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Creando...
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4 mr-2" />
                    Crear Proyecto
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {projects.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <BookOpen className="h-16 w-16 text-muted-foreground mb-4" />
          <h2 className="text-xl font-medium mb-2">No tienes proyectos aún</h2>
          <p className="text-muted-foreground mb-6">
            Crea tu primer proyecto para comenzar a organizar tu historia
          </p>
          <Button onClick={() => setOpenDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Nuevo Proyecto
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {projects.map((project) => (
            <Card key={project.id} className="overflow-hidden border border-border hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <CardTitle className="truncate">{project.title}</CardTitle>
                <CardDescription className="flex items-center gap-2">
                  <span className="font-medium">{project.genre}</span>
                  {project.overall_status && (
                    <span className="px-2 py-0.5 bg-primary/10 text-primary text-xs rounded-full">
                      {project.overall_status}
                    </span>
                  )}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground line-clamp-3 mb-2">
                  {project.logline || "Sin premisa/sinopsis"}
                </p>
                {project.theme && (
                  <div className="text-xs text-muted-foreground mt-2">
                    <span className="font-medium">Tema:</span> {project.theme}
                  </div>
                )}
                {project.target_audience && (
                  <div className="text-xs text-muted-foreground">
                    <span className="font-medium">Audiencia:</span> {project.target_audience}
                  </div>
                )}
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => navigateToProject(project.id)}>
                  Ver proyecto
                </Button>
                <div className="flex space-x-2">
                  <Button variant="ghost" size="icon" onClick={() => navigateToProject(project.id)}>
                    <Edit className="h-4 w-4" />
                  </Button>

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setProjectToDelete(project.id)}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle className="flex items-center">
                          <AlertTriangle className="h-5 w-5 text-destructive mr-2" />
                          Eliminar proyecto
                        </AlertDialogTitle>
                        <AlertDialogDescription>
                          ¿Estás seguro de que deseas eliminar "{project.title}"? Esta acción no se puede deshacer y eliminará todos los personajes y módulos asociados.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancelar</AlertDialogCancel>
                        <AlertDialogAction
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                          onClick={() => handleDeleteProject(project.id)}
                          disabled={isDeleting}
                        >
                          {isDeleting ? (
                            <>
                              <Loader2 className="h-4 w-4 animate-spin mr-2" />
                              Eliminando...
                            </>
                          ) : (
                            "Eliminar"
                          )}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default ProjectsPage;
