import { useState } from 'react';
import { useFirebase } from '@/contexts/FirebaseContext';
import { Character } from '@/types/character';
import { updateCharacter, create<PERSON><PERSON>cter } from '@/lib/firebase/firestore/characters';
import { useToast } from '@/hooks/use-toast';

// Hook para manejar el estado del formulario de personaje
export const useCharacterState = (initialCharacter: Character | Omit<Character, 'id'>) => {
  const [character, setCharacter] = useState<Character | Omit<Character, 'id'>>(initialCharacter);

  // Manejadores básicos de cambios en campos simples
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCharacter(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setCharacter(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Manejadores para tipos de campos específicos
  const handleDateChange = (date: Date | undefined) => {
    setCharacter(prev => ({
      ...prev,
      fecha_nacimiento: date
    }));
  };

  const handleImageChange = (url: string) => {
    setCharacter(prev => ({
      ...prev,
      image_url: url,
      profilePicture: url // Para compatibilidad con versiones anteriores
    }));
  };

  const handleColorChange = (color: string) => {
    setCharacter(prev => ({
      ...prev,
      color_tag: color
    }));
  };

  // Manejadores para campos de tipo array
  const handleArrayChange = (name: string, values: string[]) => {
    setCharacter(prev => ({
      ...prev,
      [name]: values
    }));
  };

  const handleAddToArray = (name: string, value: string) => {
    if (!value.trim()) return;

    setCharacter(prev => {
      const currentArray = prev[name] as string[] || [];
      if (!currentArray.includes(value.trim())) {
        return {
          ...prev,
          [name]: [...currentArray, value.trim()]
        };
      }
      return prev;
    });
  };

  const handleRemoveFromArray = (name: string, value: string) => {
    setCharacter(prev => {
      const currentArray = prev[name] as string[] || [];
      return {
        ...prev,
        [name]: currentArray.filter(item => item !== value)
      };
    });
  };

  // Manejador para campos de tipo slider
  const handleSliderChange = (name: string, value: number[]) => {
    setCharacter(prev => ({
      ...prev,
      [name]: value[0]
    }));
  };

  const resetForm = (newState: Character | Omit<Character, 'id'>) => {
    setCharacter(newState);
  };

  return {
    character,
    setCharacter,
    handleInputChange,
    handleSelectChange,
    handleDateChange,
    handleImageChange,
    handleColorChange,
    handleArrayChange,
    handleAddToArray,
    handleRemoveFromArray,
    handleSliderChange,
    resetForm
  };
};

// Hook para manejar la validación del formulario
export const useCharacterValidation = () => {
  const { toast } = useToast();

  const validateCharacter = (character: Character | Omit<Character, 'id'>, projectId?: string, isEditMode = false, currentUser?: any) => {
    if (!currentUser && !isEditMode) {
      toast({
        title: "Inicio de sesión requerido",
        description: "Debes iniciar sesión para guardar personajes.",
        variant: "destructive",
        duration: 3000,
      });
      return false;
    }

    if (!projectId && !isEditMode) {
      toast({
        title: "Proyecto requerido",
        description: "Debes seleccionar un proyecto para guardar este personaje.",
        variant: "destructive",
        duration: 3000,
      });
      return false;
    }

    if (!character.name) {
      toast({
        title: "Nombre requerido",
        description: "Por favor, añade un nombre a tu personaje.",
        variant: "destructive",
        duration: 3000,
      });
      return false;
    }

    return true;
  };

  return { validateCharacter };
};

// Hook para manejar la persistencia del personaje
export const useCharacterPersistence = () => {
  const { toast } = useToast();
  const [saving, setSaving] = useState(false);

  const saveCharacter = async (
    character: Character | Omit<Character, 'id'>,
    projectId: string | undefined,
    isEditMode: boolean,
    currentUser: any,
    onSuccess?: (character: Character) => void,
    initialState?: Omit<Character, 'id'>
  ) => {
    try {
      setSaving(true);

      if (isEditMode && 'id' in character && projectId) {
        const updatedCharacter = await updateCharacter(projectId, character.id, character);
        onSuccess?.(updatedCharacter as Character);
        
        toast({
          title: "Personaje actualizado",
          description: `${character.name} ha sido actualizado correctamente.`,
          duration: 3000,
        });
        
        return { success: true, character: updatedCharacter };
      } else if (projectId && currentUser) {
        const characterData = {
          ...character as Omit<Character, 'id'>,
          userId: currentUser.uid
        };
        const newCharacter = await createCharacter(projectId, characterData);
        
        toast({
          title: "Personaje guardado",
          description: `${character.name} ha sido guardado correctamente.`,
          duration: 3000,
        });
        
        return { success: true, character: newCharacter, shouldReset: true };
      }

      return { success: false };
    } catch (error) {
      toast({
        title: isEditMode ? "Error al actualizar" : "Error al guardar",
        description: "No se pudo guardar el personaje. Inténtalo de nuevo.",
        variant: "destructive",
        duration: 3000,
      });
      console.error("Error guardando/actualizando personaje:", error);
      return { success: false };
    } finally {
      setSaving(false);
    }
  };

  return { saving, saveCharacter };
};

// Hook principal que combina los hooks anteriores
interface UseCharacterFormProps {
  editMode?: boolean;
  characterToEdit?: Character;
  onCharacterUpdated?: (character: Character) => void;
  projectId?: string;
}

export const useCharacterForm = ({
  editMode = false,
  characterToEdit,
  onCharacterUpdated,
  projectId
}: UseCharacterFormProps) => {
  const { currentUser } = useFirebase();

  const initialCharacterState: Omit<Character, 'id'> = {
    name: '',
    alias: [],
    image_url: '',
    color_tag: '#6366F1', // Default indigo color
    role: '',
    descripcion_fisica: '',
    personalidad: [],
    traits: [],
    motivacion: '',
    internal_conflict: '',
    external_conflict: '',
    bio: '',
    arc_summary: '',
    initial_state: '',
    final_state: '',
    fecha_nacimiento: null,
    notas_adicionales: '',
    projectId: projectId || 'default',

    // Legacy fields with default values
    age: '',
    occupation: '',
    physicalDescription: '',
    personality: '',
    background: '',
    motivation: '',
    strength: 50,
    intelligence: 50,
    charisma: 50,
    resilience: 50
  };

  // Usar los hooks especializados
  const characterState = useCharacterState(
    editMode && characterToEdit ? characterToEdit : initialCharacterState
  );
  
  const { validateCharacter } = useCharacterValidation();
  const { saving, saveCharacter } = useCharacterPersistence();

  // Función principal para guardar el personaje
  const saveCharacterToFirestore = async () => {
    // Validar el personaje
    if (!validateCharacter(characterState.character, projectId, editMode, currentUser)) {
      return false;
    }

    // Guardar el personaje
    const result = await saveCharacter(
      characterState.character,
      projectId,
      editMode,
      currentUser,
      onCharacterUpdated,
      initialCharacterState
    );

    // Resetear el formulario si es necesario
    if (result.success && result.shouldReset) {
      characterState.resetForm(initialCharacterState);
    }

    return result.success;
  };

  const resetForm = () => {
    characterState.resetForm(initialCharacterState);
  };

  return {
    character: characterState.character,
    saving,
    handleInputChange: characterState.handleInputChange,
    handleSelectChange: characterState.handleSelectChange,
    handleDateChange: characterState.handleDateChange,
    handleImageChange: characterState.handleImageChange,
    handleColorChange: characterState.handleColorChange,
    handleArrayChange: characterState.handleArrayChange,
    handleAddToArray: characterState.handleAddToArray,
    handleRemoveFromArray: characterState.handleRemoveFromArray,
    handleSliderChange: characterState.handleSliderChange,
    saveCharacterToFirestore,
    resetForm
  };
};