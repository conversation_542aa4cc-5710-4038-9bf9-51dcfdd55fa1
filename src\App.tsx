
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import IdeasPage from "./pages/IdeasPage";
import CharacterPage from "./pages/CharacterPage";
import CharacterDetailPage from "./pages/CharacterDetailPage";
import LocationDetailPage from "./pages/LocationDetailPage";
import AuthPage from "./pages/AuthPage";
import ProjectsPage from "./pages/ProjectsPage";
import ProjectDetailPage from "./pages/ProjectDetailPage";
import OutlineDetailPage from "./pages/OutlineDetailPage";
import { ThemeProvider } from "@/components/ThemeProvider";
import Navbar from "@/components/Navbar";
import { FirebaseProvider } from "./contexts/FirebaseContext";
import ProtectedRoute from "@/components/ProtectedRoute";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider defaultTheme="light">
      <FirebaseProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <div className="min-h-screen flex flex-col">
              <Navbar />
              <main className="flex-1">
                <Routes>
                  <Route path="/" element={<Index />} />
                  <Route path="/auth" element={<AuthPage />} />

                  {/* Rutas protegidas que requieren autenticación */}
                  <Route path="/ideas" element={
                    <ProtectedRoute>
                      <IdeasPage />
                    </ProtectedRoute>
                  } />
                  <Route path="/characters" element={
                    <ProtectedRoute>
                      <CharacterPage />
                    </ProtectedRoute>
                  } />
                  <Route path="/projects/:projectId/characters/:characterId" element={
                    <ProtectedRoute>
                      <CharacterDetailPage />
                    </ProtectedRoute>
                  } />
                  <Route path="/projects/:projectId/locations/:locationId" element={
                    <ProtectedRoute>
                      <LocationDetailPage />
                    </ProtectedRoute>
                  } />
                  <Route path="/projects" element={
                    <ProtectedRoute>
                      <ProjectsPage />
                    </ProtectedRoute>
                  } />
                  <Route path="/projects/:projectId" element={
                    <ProtectedRoute>
                      <ProjectDetailPage />
                    </ProtectedRoute>
                  } />
                  <Route path="/projects/:projectId/outlines/:outlineId" element={
                    <ProtectedRoute>
                      <OutlineDetailPage />
                    </ProtectedRoute>
                  } />

                  <Route path="*" element={<NotFound />} />
                </Routes>
              </main>
            </div>
          </BrowserRouter>
        </TooltipProvider>
      </FirebaseProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
