
import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { useFirebase } from '@/contexts/FirebaseContext';
import { getProjectById } from '@/lib/firebase/firestore/projects';
import { getProjectCharacters, delete<PERSON>haracter } from '@/lib/firebase/firestore/characters';
import { getProjectOutlines, deleteOutline } from '@/lib/firebase/firestore/outlines';
import { getProjectLocations, deleteLocation } from '@/lib/firebase/firestore/locations';
import { logAuthState, waitForAuthReady, ensureAuthenticated } from '@/lib/firebase/auth-helpers';
import { handleFirestoreError } from '@/lib/firebase/connection-manager';
import { Project } from '@/types/project';
import { Character } from '@/types/firestore-models';
import { Outline } from '@/types/outline';
import { Location } from '@/types/firestore-models';

// Import the new components
import ProjectHeader from '@/components/project/ProjectHeader';
import ProjectOverview from '@/components/project/ProjectOverview';
import CharactersTab from '@/components/project/CharactersTab';
import LocationsTab from '@/components/project/LocationsTab';
import OutlinesTab from '@/components/project/OutlinesTab';
import DevelopmentTab from '@/components/project/DevelopmentTab';
import LoadingState from '@/components/project/LoadingState';
import ProjectNotFound from '@/components/project/ProjectNotFound';

const ProjectDetailPage = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const { toast } = useToast();
  const { currentUser } = useFirebase();
  const navigate = useNavigate();

  const [project, setProject] = useState<Project | null>(null);
  const [characters, setCharacters] = useState<Character[]>([]);
  const [locations, setLocations] = useState<Location[]>([]);
  const [outlines, setOutlines] = useState<Outline[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const loadProjectData = async () => {
      if (!currentUser) {
        navigate('/auth');
        return;
      }

      // Ensure authentication is ready and token is valid
      await waitForAuthReady();
      const token = await ensureAuthenticated();
      if (!token) {
        console.error("Authentication token not available");
        toast({
          title: "Error de autenticación",
          description: "Por favor, inicia sesión nuevamente.",
          variant: "destructive",
        });
        navigate('/auth');
        return;
      }

      // Log authentication state for debugging
      await logAuthState();

      if (!projectId) {
        navigate('/projects');
        return;
      }

      try {
        setIsLoading(true);

        try {
          // Get the project data first
          const projectData = await getProjectById(projectId);

          // Verificar que el proyecto pertenece al usuario actual
          if (projectData.userId !== currentUser.uid) {
            toast({
              title: "Acceso denegado",
              description: "No tienes permiso para ver este proyecto.",
              variant: "destructive",
            });
            navigate('/projects');
            return;
          }

          setProject(projectData);

          // Log additional debugging information
          console.log("Project data loaded successfully:", {
            projectId: projectData.id,
            userId: projectData.userId,
            currentUserId: currentUser.uid
          });
        } catch (projectError) {
          // Intentar reconectar si es un error de red
          const reconnected = await handleFirestoreError(projectError);

          if (!reconnected) {
            console.error("Error loading project data:", projectError);
            toast({
              title: "Error al cargar proyecto",
              description: "No se pudo cargar la información del proyecto. Por favor, intenta de nuevo.",
              variant: "destructive",
            });
            navigate('/projects');
            return;
          } else {
            // Si se reconectó, intentar cargar el proyecto nuevamente
            const projectData = await getProjectById(projectId);
            setProject(projectData);
          }
        }

        // Cargar datos en paralelo para mejorar el rendimiento
        const loadDataPromises = [
          // Cargar los personajes
          (async () => {
            try {
              const projectCharacters = await getProjectCharacters(projectId);
              console.log(`Loaded ${projectCharacters.length} characters`);
              setCharacters(projectCharacters);
            } catch (characterError) {
              console.error("Error loading characters:", characterError);
              await handleFirestoreError(characterError);
              setCharacters([]);
            }
          })(),

          // Cargar las localizaciones
          (async () => {
            try {
              const projectLocations = await getProjectLocations(projectId);
              console.log(`Loaded ${projectLocations.length} locations`);
              setLocations(projectLocations);
            } catch (locationError) {
              console.error("Error loading locations:", locationError);
              await handleFirestoreError(locationError);
              setLocations([]);
            }
          })(),

          // Cargar las escaletas
          (async () => {
            try {
              const projectOutlines = await getProjectOutlines(projectId);
              console.log(`Loaded ${projectOutlines.length} outlines`);
              setOutlines(projectOutlines);
            } catch (outlineError) {
              console.error("Error loading outlines:", outlineError);
              await handleFirestoreError(outlineError);
              setOutlines([]);
            }
          })()
        ];

        // Esperar a que todas las promesas se resuelvan
        await Promise.allSettled(loadDataPromises);

      } catch (error) {
        console.error("Error general loading project data:", error);
        toast({
          title: "Error al cargar proyecto",
          description: "No se pudo cargar la información del proyecto. Por favor, intenta de nuevo.",
          variant: "destructive",
        });
        navigate('/projects');
      } finally {
        setIsLoading(false);
      }
    };

    loadProjectData();
  }, [projectId, currentUser, navigate, toast]);

  const handleCharacterUpdated = (updatedCharacter: Character) => {
    setCharacters(prev =>
      prev.map(character =>
        character.id === updatedCharacter.id ? updatedCharacter : character
      )
    );
  };

  const handleOutlineUpdated = (updatedOutline: Outline) => {
    const outlineExists = outlines.some(outline => outline.id === updatedOutline.id);

    if (outlineExists) {
      // Actualizar la escaleta existente
      setOutlines(prev =>
        prev.map(outline =>
          outline.id === updatedOutline.id ? updatedOutline : outline
        )
      );
    } else {
      // Añadir la nueva escaleta
      setOutlines(prev => [...prev, updatedOutline]);
    }
  };

  const handleDeleteCharacter = async (characterId: string) => {
    try {
      setIsDeleting(true);

      if (!projectId) {
        throw new Error("Project ID is required");
      }

      // Delete the character from Firestore
      await deleteCharacter(projectId, characterId);

      // Update the UI by removing the character from the state
      setCharacters(prev => prev.filter(character => character.id !== characterId));

      toast({
        title: "Personaje eliminado",
        description: "El personaje ha sido eliminado correctamente.",
      });
    } catch (error) {
      console.error("Error deleting character:", error);
      toast({
        title: "Error al eliminar",
        description: "No se pudo eliminar el personaje. Por favor, intenta de nuevo.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleLocationUpdated = (updatedLocation: Location) => {
    const locationExists = locations.some(location => location.id === updatedLocation.id);

    if (locationExists) {
      // Actualizar la localización existente
      setLocations(prev =>
        prev.map(location =>
          location.id === updatedLocation.id ? updatedLocation : location
        )
      );
    } else {
      // Añadir la nueva localización
      setLocations(prev => [...prev, updatedLocation]);
    }
  };

  const handleDeleteLocation = (locationId: string) => {
    setLocations(prev => prev.filter(location => location.id !== locationId));
  };

  const handleDeleteOutline = async (outlineId: string) => {
    try {
      setIsDeleting(true);

      if (!projectId) {
        throw new Error("Project ID is required");
      }

      await deleteOutline(projectId, outlineId);

      // Actualizar la lista de escaletas
      setOutlines(prev => prev.filter(outline => outline.id !== outlineId));

      toast({
        title: "Escaleta eliminada",
        description: "La escaleta ha sido eliminada correctamente.",
      });
    } catch (error) {
      console.error("Error deleting outline:", error);
      toast({
        title: "Error al eliminar",
        description: "No se pudo eliminar la escaleta. Por favor, intenta de nuevo.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  if (isLoading) {
    return <LoadingState />;
  }

  if (!project) {
    return <ProjectNotFound />;
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-4">
        <ProjectHeader project={project} />
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-4">
        <TabsList className="mb-8">
          <TabsTrigger value="overview">Vista general</TabsTrigger>
          <TabsTrigger value="characters">Personajes</TabsTrigger>
          <TabsTrigger value="locations">Localizaciones</TabsTrigger>
          <TabsTrigger value="documentation" disabled>Documentación</TabsTrigger>
          <TabsTrigger value="outline">Escaletas</TabsTrigger>
          <TabsTrigger value="chapters" disabled>Capítulos</TabsTrigger>
          <TabsTrigger value="assistant" disabled>Asistente</TabsTrigger>
        </TabsList>

        {/* Vista general */}
        <TabsContent value="overview">
          <ProjectOverview
            characterCount={characters.length}
            locationCount={locations.length}
            outlineCount={outlines.length}
            onModuleClick={setActiveTab}
          />
        </TabsContent>

        {/* Personajes */}
        <TabsContent value="characters">
          <CharactersTab
            characters={characters}
            projectId={projectId || ''}
            onCharacterUpdated={handleCharacterUpdated}
            onDeleteCharacter={handleDeleteCharacter}
            isDeleting={isDeleting}
          />
        </TabsContent>

        {/* Localizaciones */}
        <TabsContent value="locations">
          <LocationsTab
            locations={locations}
            projectId={projectId || ''}
            onLocationUpdated={handleLocationUpdated}
            onDeleteLocation={handleDeleteLocation}
            isDeleting={isDeleting}
          />
        </TabsContent>

        {/* Escaletas */}
        <TabsContent value="outline">
          <OutlinesTab
            outlines={outlines}
            projectId={projectId || ''}
            onOutlineUpdated={handleOutlineUpdated}
            onDeleteOutline={handleDeleteOutline}
            isDeleting={isDeleting}
          />
        </TabsContent>

        {/* Contenido para los otros módulos (deshabilitados por ahora) */}
        {['documentation', 'chapters', 'assistant'].map((moduleId) => (
          <TabsContent key={moduleId} value={moduleId}>
            <DevelopmentTab moduleId={moduleId} />
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};

export default ProjectDetailPage;
